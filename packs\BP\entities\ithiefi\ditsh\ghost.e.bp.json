{"format_version": "1.21.90", "minecraft:entity": {"description": {"identifier": "ditsh:ghost", "is_spawnable": true, "is_summonable": true}, "component_groups": {}, "events": {"ditsh:on_hit": {"queue_command": {"command": "playsound mob.ditsh.ghost.hit @a ~ ~ ~"}}}, "components": {"minecraft:is_hidden_when_invisible": {}, "minecraft:experience_reward": {"on_death": "query.last_hit_by_player ? 12 + (query.equipment_count * Math.Random(1,3)) : 0"}, "minecraft:type_family": {"family": ["ditsh", "ghost", "mob", "monster"]}, "minecraft:collision_box": {"width": 0.5, "height": 1.2}, "minecraft:health": {"value": 30, "max": 30}, "minecraft:variable_max_auto_step": {"base_value": 1.0625, "jump_prevented_value": 1.0625}, "minecraft:flying_speed": {"value": 0.15}, "minecraft:movement.hover": {}, "minecraft:navigation.hover": {"can_open_doors": true, "can_open_iron_doors": true, "avoid_damage_blocks": true, "avoid_water": false, "can_path_over_water": true, "can_path_over_lava": true, "can_sink": false, "is_amphibious": true, "can_path_from_air": true}, "minecraft:behavior.float": {"priority": 0}, "minecraft:attack": {"damage": 5}, "minecraft:behavior.melee_box_attack": {"priority": 1, "track_target": true, "horizontal_reach": 0.8, "cooldown_time": 1, "can_spread_on_fire": true, "speed_multiplier": 1.5, "on_attack": {"event": "ditsh:on_hit", "target": "self"}}, "minecraft:behavior.nearest_prioritized_attackable_target": {"priority": 0, "target_search_height": -1, "reselect_targets": true, "cooldown": 0, "attack_interval": 0, "must_reach": false, "must_see": false, "reevaluate_description": true, "entity_types": [{"priority": 0, "filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "max_dist": 256}]}, "minecraft:behavior.hurt_by_target": {"priority": 1, "entity_types": {"filters": {"test": "is_family", "subject": "other", "value": "player"}, "must_see": false, "reevaluate_description": false, "max_dist": 256}}, "minecraft:behavior.random_look_around": {"priority": 5}, "minecraft:behavior.random_hover": {"priority": 4, "hover_height": [1, 3]}, "minecraft:follow_range": {"value": 1024, "max": 1024}, "minecraft:jump.static": {}, "minecraft:can_fly": {}, "minecraft:pushable": {}, "minecraft:physics": {"has_collision": false}, "minecraft:conditional_bandwidth_optimization": {}, "minecraft:ambient_sound_interval": {"event_name": "ambient", "range": 2, "value": 4}}}}