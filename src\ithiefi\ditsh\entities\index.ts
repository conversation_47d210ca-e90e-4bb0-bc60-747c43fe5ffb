import { Player, world } from "@minecraft/server";
import { setEntityToCardinalDirection } from "../utilities/rotation";
import {
  entitiesWithMusic,
  continueMusicForEntity,
  stopMusicForEntity,
  playMusicForEntity,
  resetPlayerMusic
} from "./entitiesWithMusic";
import { mamaTattletailTeleportHandler } from "./mamaTattletail";
import { mxCheckForWallHit } from "./mx";

/**
 * @fileoverview Entity Event Listeners for DitSH Add-On
 *
 * This module initializes and manages all entity-related event listeners for the add-on.
 * It handles entity spawning, loading, and data-driven entity events to manage entity behavior
 * and music systems.
 *
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * Initializes all entity-related event listeners for the DitSH add-on.
 *
 * @description This function sets up event listeners for various entity events including:
 * - Entity spawn events (for door rotation)
 * - Entity load events (for music continuation and player music reset)
 * - Data-driven entity trigger events (for music control via entity behavior)
 *
 * @remarks
 * This function should be called once during add-on initialization to register
 * all necessary event handlers. The listeners handle:
 *
 * **Entity Spawn Events:**
 * - Sets door entities (ditsh:door1, ditsh:door2) to cardinal directions
 *
 * **Entity Load Events:**
 * - Continues music for entities that have associated music tracks
 * - Resets music state for players when they load into the world
 *
 * **Data-Driven Entity Trigger Events:**
 * - Starts/maintains music when entities trigger "ditsh:start_chase_music" or "ditsh:maintain_chase_music"
 * - Stops music when entities trigger "ditsh:stop_chase_music" or "ditsh:on_death"
 *
 * @example
 * ```typescript
 * // Called in main.ts to initialize all entity systems
 * import { initEntityListeners } from "./entities/index";
 *
 * initEntityListeners();
 * ```
 */
export function initEntityListeners(): void {
  // General entity spawn event listener
  world.afterEvents.entitySpawn.subscribe((data) => {
    const entity = data.entity;
    const typeId: string = entity.typeId;
    if (typeId === "ditsh:door1" || typeId === "ditsh:door2") {
      setEntityToCardinalDirection(entity);
    }
  });

  // General entity load event listener
  world.afterEvents.entityLoad.subscribe((data) => {
    const entity = data.entity;
    const typeId: string = entity.typeId;
    if (entitiesWithMusic.has(typeId)) {
      continueMusicForEntity(entity, entitiesWithMusic.get(typeId)!);
    } else if (entity instanceof Player) {
      resetPlayerMusic(entity);
    }
  });

  // General data driven entity event listener
  world.afterEvents.dataDrivenEntityTrigger.subscribe((data) => {
    const entity = data.entity;
    const typeId: string = entity.typeId;
    const eventId: string = data.eventId;
    switch (eventId) {
      case "ditsh:start_chase_music":
        playMusicForEntity(entity, entitiesWithMusic.get(typeId)!);
        break;
      case "ditsh:maintain_chase_music":
        playMusicForEntity(entity, entitiesWithMusic.get(typeId)!);
        break;
      case "ditsh:stop_chase_music":
        stopMusicForEntity(entity, entitiesWithMusic.get(typeId)!);
        break;
      case "ditsh:on_death":
        stopMusicForEntity(entity, entitiesWithMusic.get(typeId)!);
        break;
      case "ditsh:teleport":
        mamaTattletailTeleportHandler(entity);
        break;
      case "ditsh:mx_check_for_wall_hit":
        mxCheckForWallHit(entity);
        break;
      default:
        break;
    }
  });
}
