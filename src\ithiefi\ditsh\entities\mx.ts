import { Entity, system, Vector3 } from "@minecraft/server";

/**
 * Checks if the MX entity is about to hit a wall and plays a sound if so.
 * and stops the dash attack.
 *
 * @param {Entity} mx - The MX entity to check.
 */
export function mxCheckForWallHit(mx: Entity) {
  try {
    const isDashing = mx.getProperty("ditsh:dashing") as boolean;
    if (!isDashing) return;

    const location: Vector3 = { x: mx.location.x, y: mx.location.y + 2, z: mx.location.z };
    const direction = mx.getViewDirection();
    // 3 blocks offset in front of mx
    const offset: Vector3 = { x: location.x + direction.x * 3, y: location.y, z: location.z + direction.z * 3 };
    const block = mx.dimension.getBlock(offset);
    if (block && !block.isAir) {
      mx.triggerEvent("ditsh:on_dash_attack");
      mx.dimension.playSound("mob.ditsh.mx.hit_wall", location);
    }
  } catch (e) {} // Handle errors silently
  return;
}

export async function mxJump(mx: Entity) {
  try {
    mx.setProperty("ditsh:jumping", true);
    await system.waitTicks()
  } catch (e) {} // Handle errors silently
  return;
}
