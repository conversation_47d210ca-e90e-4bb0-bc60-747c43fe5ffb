import { Entity, Player, system } from "@minecraft/server";

/**
 * @fileoverview Entity Music System for DitSH Add-On
 *
 * This module manages background music playback for specific entities in the game.
 * It handles music playback, stopping, and persistence across world reloads.
 *
 * <AUTHOR>
 * @version 1.0.0
 */

/**
 * Map of entity type IDs to their associated music track identifiers.
 *
 * @description Defines which entities should play music and what music they should play.
 * The key is the entity type ID, and the value is the sound identifier for the music track.
 *
 * @example
 * ```typescript
 * // Check if an entity has associated music
 * if (entitiesWithMusic.has(entity.typeId)) {
 *   const musicTrack = entitiesWithMusic.get(entity.typeId);
 *   playMusicForEntity(entity, musicTrack);
 * }
 * ```
 */
export const entitiesWithMusic = new Map<string, string>([
  ["ditsh:ao_oni", "mob.ditsh.ao_oni.chase"],
  ["ditsh:armless", "mob.ditsh.armless.chase"],
  ["ditsh:headless_horseman", "mob.ditsh.headless_horseman.chase"],
  ["ditsh:herobrine", "mob.ditsh.herobrine.chase"],
  ["ditsh:jack_black", "mob.ditsh.jack_black.chase"],
  ["ditsh:jeff", "mob.ditsh.jeff.chase"],
  ["ditsh:mac_tonight", "mob.ditsh.mac_tonight.chase"],
  ["ditsh:mama_tattletail", "mob.ditsh.mama_tattletail.chase"],
  ["ditsh:murder_monkey", "mob.ditsh.murder_monkey.chase"],
  ["ditsh:mx", "mob.ditsh.mx.chase"]
]);

/**
 * Plays background music for nearby players when a specific entity is present.
 *
 * @param entity - The entity that triggers the music playback
 * @param music - The sound identifier for the music track to play
 *
 * @description This function finds all players within a 256-block radius of the entity
 * and starts playing the specified music track for them. It prevents duplicate music
 * playback by checking if the music is already playing for each player.
 *
 * @remarks
 * - Music is played using the `playsound` command with default volume (1.0) and pitch (1.0)
 * - Player dynamic properties are used to track music playback state
 * - The function is safe to call repeatedly as it prevents duplicate music playback
 *
 * @example
 * ```typescript
 * const aoOniEntity = dimension.getEntities({ type: "ditsh:ao_oni" })[0];
 * if (aoOniEntity) {
 *   playMusicForEntity(aoOniEntity, "mob.ditsh.ao_oni.chase");
 * }
 * ```
 */
export function playMusicForEntity(entity: Entity, music: string): void {
  const nearbyPlayers: Player[] = getNearbyPlayers(entity, 256);

  for (const player of nearbyPlayers) {
    const isCurrentlyPlaying: boolean = isPlayerPlayingMusic(player, entity.typeId);

    if (!isCurrentlyPlaying) {
      player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
      player.setDynamicProperty(`${entity.typeId}_music`, true);
    }
  }
}

/**
 * Stops background music for nearby players when a specific entity is no longer present.
 *
 * @param entity - The entity that was triggering the music playback
 * @param music - The sound identifier for the music track to stop
 *
 * @description This function finds all players within a 256-block radius of the entity
 * and stops the specified music track for them if it's currently playing. It updates
 * the player's dynamic properties to reflect that the music is no longer playing.
 *
 * @remarks
 * - Music is stopped using the `stopsound` command targeting the specific sound
 * - Player dynamic properties are updated to track that music is no longer playing
 * - Only stops music for players who are currently playing the specified track
 *
 * @example
 * ```typescript
 * // Stop music when entity dies or despawns
 * world.afterEvents.entityDie.subscribe((event) => {
 *   if (event.deadEntity.typeId === "ditsh:ao_oni") {
 *     stopMusicForEntity(event.deadEntity, "mob.ditsh.ao_oni.chase");
 *   }
 * });
 * ```
 */
export function stopMusicForEntity(entity: Entity, music: string): void {
  const nearbyPlayers: Player[] = getNearbyPlayers(entity, 256);

  for (const player of nearbyPlayers) {
    const isCurrentlyPlaying: boolean = isPlayerPlayingMusic(player, entity.typeId);

    if (isCurrentlyPlaying) {
      player.runCommand(`stopsound @s ${music}`);
      player.setDynamicProperty(`${entity.typeId}_music`, false);
    }
  }
}

/**
 * Continues playing music for an entity after world reload or entity respawn.
 *
 * @param entity - The entity that should continue playing music
 * @param music - The sound identifier for the music track to continue playing
 *
 * @description This function is called when entities are loaded back into the world
 * (such as after a world reload or chunk loading). It checks if the entity was
 * previously playing music and resumes playback for nearby players.
 *
 * @remarks
 * - Waits 140 ticks (7 seconds) before starting music to allow world stabilization
 * - Only continues music if the entity's "ditsh:playing_music" property is true
 * - Prevents duplicate music playback by checking current player music state
 * - This is an async function that uses system.waitTicks for timing control
 *
 * @example
 * ```typescript
 * // Called during entity load events
 * world.afterEvents.entityLoad.subscribe((event) => {
 *   const entity = event.entity;
 *   if (entitiesWithMusic.has(entity.typeId)) {
 *     const musicTrack = entitiesWithMusic.get(entity.typeId)!;
 *     continueMusicForEntity(entity, musicTrack);
 *   }
 * });
 * ```
 */
export async function continueMusicForEntity(entity: Entity, music: string): Promise<void> {
  const playMusic: boolean = entity.getProperty("ditsh:playing_music") as boolean;

  if (playMusic) {
    await system.waitTicks(140);
    const nearbyPlayers: Player[] = getNearbyPlayers(entity, 256);

    for (const player of nearbyPlayers) {
      const isCurrentlyPlaying: boolean = isPlayerPlayingMusic(player, entity.typeId);

      if (!isCurrentlyPlaying) {
        player.runCommand(`playsound ${music} @s ~ ~ ~ 1.0 1.0`);
        player.setDynamicProperty(`${entity.typeId}_music`, true);
      }
    }
  }
}

/**
 * Resets all entity music for a player when they rejoin the world.
 *
 * @param player - The player whose music state should be reset
 *
 * @description This function is called when a player loads into the world
 * (such as after rejoining or respawning). It stops all entity-related music
 * that might be playing for the player and resets their music state properties.
 *
 * @remarks
 * - Iterates through all registered entities with music in the entitiesWithMusic map
 * - Stops any currently playing music tracks for the player
 * - Resets all music-related dynamic properties to false
 * - Prevents music from continuing to play inappropriately after player reconnection
 *
 * @example
 * ```typescript
 * // Called during player load events
 * world.afterEvents.entityLoad.subscribe((event) => {
 *   if (event.entity instanceof Player) {
 *     resetPlayerMusic(event.entity);
 *   }
 * });
 * ```
 */
export function resetPlayerMusic(player: Player): void {
  for (const [entityTypeId, music] of entitiesWithMusic) {
    const isCurrentlyPlaying: boolean = isPlayerPlayingMusic(player, entityTypeId);

    if (isCurrentlyPlaying) {
      player.runCommand(`stopsound @s ${music}`);
      player.setDynamicProperty(`${entityTypeId}_music`, false);
    }
  }
}

/**
 * Retrieves all players within a specified range of an entity.
 *
 * @param entity - The entity to search around
 * @param range - The maximum distance in blocks to search for players
 * @returns An array of Player objects within the specified range
 *
 * @description This helper function uses the dimension's getPlayers method
 * to find all players within a circular area around the entity's location.
 *
 * @remarks
 * - Uses the entity's current location as the center point
 * - Range is measured in blocks using Euclidean distance
 * - Returns an empty array if no players are found within range
 *
 * @example
 * ```typescript
 * const nearbyPlayers = getNearbyPlayers(aoOniEntity, 64);
 * console.log(`Found ${nearbyPlayers.length} players nearby`);
 * ```
 */
function getNearbyPlayers(entity: Entity, range: number): Player[] {
  const players: Player[] = entity.dimension.getPlayers({ location: entity.location, maxDistance: range });
  return players;
}

/**
 * Checks if a player is currently playing music for a specific entity type.
 *
 * @param player - The player to check
 * @param entityTypeId - The entity type ID to check music state for
 * @returns True if the player is currently playing music for the specified entity type
 *
 * @description This helper function checks the player's dynamic properties
 * to determine if music is currently playing for a specific entity type.
 * The property name follows the pattern: `${entityTypeId}_music`.
 *
 * @remarks
 * - Returns false if the dynamic property doesn't exist or is falsy
 * - Dynamic properties persist across game sessions
 * - Used to prevent duplicate music playback
 *
 * @example
 * ```typescript
 * if (!isPlayerPlayingMusic(player, "ditsh:ao_oni")) {
 *   // Start playing music for this entity type
 *   playMusicForEntity(entity, "mob.ditsh.ao_oni.chase");
 * }
 * ```
 */
function isPlayerPlayingMusic(player: Player, entityTypeId: string): boolean {
  const playingMusic: boolean = player.getDynamicProperty(`${entityTypeId}_music`) as boolean;
  return playingMusic ?? false;
}
