{"format_version": "1.10.0", "animation_controllers": {"controller.animation.ithiefi_ditsh_mx.attack_type": {"initial_state": "default", "states": {"default": {"transitions": [{"select_attack": "q.has_target"}]}, "select_attack": {"on_entry": ["/event entity @s ditsh:select_attack"], "transitions": [{"default": "true"}]}}}, "controller.animation.ithiefi_ditsh_mx.dash_attack": {"initial_state": "default", "states": {"default": {"transitions": [{"dash_attack": "q.property('ditsh:dashing') == true && v.attack_time > 0"}]}, "dash_attack": {"on_entry": ["/event entity @s ditsh:on_dash_attack"], "transitions": [{"default": "v.attack_time <= 0"}]}}}}}